.phone-input__container {
  position: relative;
  width: 100%;
}

.phone-input__label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.phone-input__wrapper {
  display: flex;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: white;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.phone-input__wrapper:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.phone-input__wrapper--error {
  border-color: #ef4444;
}

.phone-input__wrapper--error:focus-within {
  border-color: #ef4444;
  box-shadow: 0 0 0 1px #ef4444;
}

.phone-input__wrapper--disabled {
  background-color: #f9fafb;
  opacity: 0.6;
  cursor: not-allowed;
}

/* Country Code Dropdown */
.country-code__dropdown {
  position: relative;
  flex-shrink: 0;
  z-index: 1001;
}

.country-code__trigger {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background: none;
  border: none;
  cursor: pointer;
  gap: 0.5rem;
  min-width: 120px;
  border-right: 1px solid #e5e7eb;
  transition: background-color 0.2s ease-in-out;
}

.country-code__trigger:hover:not(:disabled) {
  background-color: #f9fafb;
}

.country-code__trigger:disabled {
  cursor: not-allowed;
}

.country-code__flag-img {
  width: 20px;
  height: 15px;
  object-fit: cover;
  border-radius: 2px;
}

.country-code__value {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.country-code__arrow {
  font-size: 0.75rem;
  color: #6b7280;
  transition: transform 0.2s ease-in-out;
  margin-left: auto;
}

.country-code__arrow--open {
  transform: rotate(180deg);
}

/* Simple Dropdown */
.simple-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 9999;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 2px;
  min-width: 250px;
}

.simple-dropdown-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  cursor: pointer;
  gap: 0.75rem;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease-in-out;
}

.simple-dropdown-item:last-child {
  border-bottom: none;
}

.simple-dropdown-item:hover {
  background-color: #f9fafb;
}

.simple-dropdown-item.active {
  background-color: #eff6ff;
  color: #1d4ed8;
}

.flag-img {
  width: 20px;
  height: 15px;
  object-fit: cover;
  border-radius: 2px;
  flex-shrink: 0;
}

.dial-code {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
  min-width: 60px;
}

.country-name {
  color: #6b7280;
  font-size: 0.875rem;
  flex: 1;
}



/* Phone Input Field */
.phone-input__field {
  flex: 1;
  padding: 0.75rem;
  border: none;
  outline: none;
  font-size: 0.875rem;
  background: transparent;
}

.phone-input__field:disabled {
  cursor: not-allowed;
}

.phone-input__field::placeholder {
  color: #9ca3af;
}

/* Error Message */
.phone-input__error {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #ef4444;
}

/* Responsive Design */
@media (max-width: 640px) {
  .country-code__trigger {
    min-width: 100px;
    padding: 0.5rem;
  }
  
  .country-code__value {
    font-size: 0.75rem;
  }
  
  .phone-input__field {
    padding: 0.5rem;
    font-size: 0.875rem;
  }
  
  .country-name {
    display: none;
  }
}

/* Custom scrollbar for dropdown */
.country-code__list::-webkit-scrollbar {
  width: 6px;
}

.country-code__list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.country-code__list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.country-code__list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
