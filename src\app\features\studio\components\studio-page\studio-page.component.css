@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');

:host {
    --header-height: 3rem;
    --font-medium: 500;

    --first-color: #5361ff;
    --white-color: #fafaff;
    --dark-color: #2a3b47;
    --text-color: #697477;

    --body-font: 'Montserrat', sans-serif;
    --big-font-size: 6.25rem;
    --h2-font-size: 1.25rem;
    --normal-font-size: .938rem;
    --small-font-size: .813rem;

    --mb1: .5rem;
    --mb2: 1rem;
    --mb3: 1.5rem;
    --mb4: 2rem;

    --z-fixed: 100;
}

@media screen and (min-width: 768px) {
    :host {
        --big-font-size: 7rem;
        --h2-font-size: 2rem;
        --normal-font-size: 1rem;
        --small-font-size: .875rem;
    }
}

@media screen and (max-width: 768px) {
    :host {
        --big-font-size: 2.5rem;
        --h2-font-size: 2rem;
        --normal-font-size: 1rem;
        --small-font-size: .875rem;
    }
}

*, *::before, *::after { box-sizing: border-box; }

:host {
    display: block;
    margin: var(--header-height) 0 0 0;
    font-family: var(--body-font);
    font-size: var(--normal-font-size);
    font-weight: var(--font-medium);
    color: var(--text-color);
    line-height: 1.6;
}

h1, h2, p { margin: 0; }
ul { margin: 0; padding: 0; list-style: none; }
a { text-decoration: none; color:black; }
img { max-width: 100%; height: auto; display: block; }

.section { padding: 3rem 0; }

.section-title {
    position: relative;
    font-size: var(--h2-font-size);
    color: var(--dark-color);
    margin: var(--mb4) 0;
    text-align: center;
    font-weight: 700;
}

.section-title::after {
    position: absolute;
    content: '';
    width: 32px;
    height: 0.18rem;
    left: 0;
    right: 0;
    margin: auto;
    top: 3rem;
    background-color: var(--first-color);
}

.bd-grid {
    max-width: 1024px;
    display: grid;
    grid-template-columns: 100%;
    grid-column-gap: 2rem;
    width: calc(100% - 2rem);
    margin-left: var(--mb2);
    margin-right: var(--mb2);
}

/*HEADER*/

.l-header {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: var(--z-fixed);
    background-color: var(--first-color);
}

.nav {
    height: var(--header-height);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-item { margin-bottom: var(--mb4); }
.nav-link { position: relative; color: var(--dark-color); }
.nav-link:hover { color: var(--first-color); }
.nav-logo { color: var(--white-color); }
.nav-toggle { color: var(--white-color); font-size: 1.5rem; cursor: pointer; }

.active::after {
    position: absolute;
    content: '';
    width: 100%;
    height: 0.18rem;
    left: 0;
    top: 2rem;
    background-color: var(--first-color);
}

@media screen and (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: var(--header-height);
        right: -100%;
        width: 80%;
        height: 100%;
        padding: 2rem;
        background-color: rgba(255, 255, 255, .3);
        transition: .5s;
        backdrop-filter: blur(10px);
    }

    .show { right: 0; }
}

/*HOME*/

.home { position: relative; background-color: var(--first-color); overflow: hidden; }
.home-container { height: calc(100vh - var(--header-height)); row-gap: 5rem; }
.home-title {
    align-self: flex-end;
    font-size: var(--big-font-size);
    color: var(--white-color);
    line-height: 0.8;
    font-weight: 700;
}
.home-title span { text-shadow: 0 20px 25px rgba(0, 0, 0, .5); }

.home-scroll { align-self: flex-end; padding-bottom: var(--mb4); }
.home-scroll-link { writing-mode: vertical-lr; transform: rotate(-180deg); color: var(--white-color); }

.home-img {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 295px;
}

/*ABOUT*/

.about-container { justify-items: center; row-gap: 2rem; text-align: center; }

.about-img {
    display: flex;
    justify-content: center;
    padding-top: 1rem;
    width: 120px;
    height: 120px;
    background-color: var(--first-color);
    border-radius: 50%;
    overflow: hidden;
}

.about-img img { width: 100px; }

.about-subtitle { font-size: var(--h2-font-size); color: var(--first-color); margin-bottom: var(--mb1); font-weight: 700; }
.about-text { margin-bottom: var(--mb4); }
.about-profession { display: block; margin-bottom: var(--mb4); font-weight: 600; }
.about-social-icon { font-size: 1.4rem; margin: 0 var(--mb1); }
.about-social-icon:hover { color: var(--first-color); }

/*SKILLS*/

.skills-container { row-gap: 2rem; }
.skills-subtitle { color: var(--first-color); margin-bottom: var(--mb3); font-weight: 700; }

.skills-name {
    display: inline-block;
    font-size: var(--small-font-size);
    margin-right: var(--mb2);
    margin-bottom: var(--mb3);
    border-radius: .25rem;
    transition: .5s;
}

.skills-name:hover { background-color: var(--first-color); color: var(--white-color); }

.skills-img img { border-radius: .5rem; }

/*PORTFOLIO*/

.portfolio { background-color: var(--white-color); }
.portfolio-container { justify-items: center; row-gap: 2rem; }
.portfolio-img { position: relative; overflow: hidden; }
.portfolio-img img { border-radius: .5rem; }
.portfolio-img h1 { font-weight: 700; margin: 0.5rem 0; }
.portfolio-img p { font-weight: 500; }

.portfolio-link {
    position: absolute;
    bottom: -100%;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, .3);
    border-radius: .5rem;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: .3s;
}

.portfolio-img:hover .portfolio-link { bottom: 0; }
.portfolio-link-color { color: var(--dark-color); }

/*CONTACT*/

/* Contact Hero Section */
.contact-hero {
    text-align: center;
    margin-bottom: 4rem;
}

.contact-subtitle-text {
    font-size: 1.125rem;
    color: var(--text-color);
    margin-top: 1rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* Contact Container */
.contact-container {
    row-gap: 3rem;
    max-width: 1000px;
    margin: 0 auto;
    justify-items: center;
}

/* Contact Info Cards */
.contact-info {
    display: grid;
    gap: 1.5rem;
    max-width: 400px;
    width: 100%;
}

.contact-card {
    display: flex;
    align-items: center;
    padding: 1.25rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--first-color), #7c3aed);
}

.contact-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.contact-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--first-color), #7c3aed);
    border-radius: 0.75rem;
    color: white;
    margin-right: 1rem;
    flex-shrink: 0;
}

.contact-card-content {
    flex: 1;
}

.contact-card-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.contact-card-text {
    font-size: 0.8rem;
    color: var(--text-color);
    font-weight: 500;
}

/* Contact Form Wrapper */
.contact-form-wrapper {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: hidden;
    max-width: 600px;
    width: 100%;
}

.contact-form-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--first-color), #7c3aed, #ec4899);
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

/* Input Groups */
.input-group {
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
}

.input-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.input-wrapper {
    position: relative;
}

.contact-input {
    width: 100%;
    padding: 0.75rem 0.75rem 0.75rem 2.25rem;
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    background-color: #ffffff;
    transition: all 0.3s ease;
    outline: none;
}

.contact-input:focus {
    border-color: var(--first-color);
    box-shadow: 0 0 0 3px rgba(83, 97, 255, 0.1);
    transform: translateY(-1px);
}

.contact-input::placeholder {
    color: #94a3b8;
}

.contact-textarea {
    resize: vertical;
    min-height: 100px;
}

.input-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    pointer-events: none;
    transition: color 0.3s ease;
    width: 16px;
    height: 16px;
}

.textarea-icon {
    top: 0.75rem;
    transform: none;
}

.contact-input:focus + .input-icon,
.input-wrapper:focus-within .input-icon {
    color: var(--first-color);
}

.contact-input.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-message {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Alert Styles */
.alert {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    margin-bottom: 1.5rem;
    border: 1px solid;
}

.alert-icon {
    flex-shrink: 0;
}

.alert-success {
    background-color: #dcfce7;
    border-color: #86efac;
    color: #166534;
}

.alert-error {
    background-color: #fef2f2;
    border-color: #fca5a5;
    color: #dc2626;
}

/* Contact Button */
.contact-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, var(--first-color), #7c3aed);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.contact-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.contact-button:hover::before {
    left: 100%;
}

.contact-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(83, 97, 255, 0.4), 0 10px 10px -5px rgba(83, 97, 255, 0.2);
}

.contact-button:active {
    transform: translateY(0);
}

.contact-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.contact-button:disabled:hover {
    transform: none;
    box-shadow: none;
}

.button-text {
    position: relative;
    z-index: 1;
}

.button-icon {
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
}

.contact-button:hover .button-icon {
    transform: translateX(4px);
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #ffffff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Mobile Responsive Styles */
@media screen and (max-width: 768px) {
    .contact-hero {
        margin-bottom: 1.5rem;
    }

    .contact-subtitle-text {
        font-size: 0.875rem;
        padding: 0 1rem;
    }

    .contact-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1rem;
    }

    .contact-card {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
    }

    .contact-icon {
        margin-right: 0;
        margin-bottom: 0.75rem;
        width: 40px;
        height: 40px;
    }

    .contact-form-wrapper {
        padding: 1.25rem;
        max-width: 100%;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .contact-input {
        padding: 0.625rem 0.625rem 0.625rem 2rem;
        font-size: 0.8rem;
    }

    .input-icon {
        left: 0.625rem;
        width: 14px;
        height: 14px;
    }

    .contact-button {
        padding: 0.75rem 1.25rem;
        font-size: 0.8rem;
    }
}

@media screen and (max-width: 480px) {
    .contact-card {
        padding: 0.75rem;
    }

    .contact-form-wrapper {
        padding: 1rem;
        max-width: 100%;
    }

    .contact-input {
        padding: 0.5rem 0.5rem 0.5rem 1.75rem;
        font-size: 0.75rem;
    }

    .input-icon {
        left: 0.5rem;
        width: 12px;
        height: 12px;
    }

    .contact-button {
        padding: 0.625rem 1rem;
        font-size: 0.75rem;
    }
}

/*FOOTER*/

.footer { background-color: var(--dark-color); }
.footer-container { row-gap: 2rem; }
.footer-title { font-size: var(--normal-font-size); color: var(--white-color); margin-bottom: var(--mb2); font-weight: 700; }
.footer-link { padding: 0.25rem 0; }
.footer-link:hover { color: var(--first-color); }

.footer-social { font-size: 1.4rem; margin-right: var(--mb1); }
.footer-social:hover { color: var(--first-color); }

/*MEDIA QUERIES*/

@media screen and (min-width: 768px) {
    :host { margin: 0; }
    .section { padding-top: 4rem; }
    .section-title { margin-bottom: 3rem; }
    .section-title::after { width: 64px; top: 3rem; }

    .nav { height: calc(var(--header-height) + 1rem); }
    .nav-list { display: flex; }
    .nav-item { margin-left: var(--mb4); margin-bottom: 0; }
    .nav-toggle { display: none; }
    .nav-link { color: var(--white-color); }
    .nav-link:hover { color: var(--white-color); }
    .active::after { background-color: var(--white-color); }

    .home-container { height: 100vh; grid-template-rows: 1.7fr 1fr; row-gap: 0; }
    .home-img { width: 524px; right: 10%; }

    .about-container { grid-template-columns: repeat(2, 1fr); align-items: center; text-align: initial; padding: 4rem 0; }
    .about-img { width: 200px; height: 200px; }
    .about-img img { width: 165px; }

    .skills-container { grid-template-columns: repeat(2, 1fr); align-items: center; }

    .portfolio-container { grid-template-columns: repeat(3, 1fr); grid-template-rows: repeat(2, 1fr); column-gap: 2rem; }

    .contact-container {
        grid-template-columns: 3fr 7fr;
        gap: 2.5rem;
        align-items: start;
        max-width: 1200px;
    }

    .contact-form-wrapper {
        padding: 2rem;
        max-width: 550px;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .footer-container { grid-template-columns: repeat(3, 1fr); justify-items: center; }
}

@media screen and (min-width: 1024px) {
    .bd-grid { margin-left: auto; margin-right: auto; }
}

/*robohead*/

@layer properties {
@property --elh {
/* eye left height   */
  syntax: '<number>';
  inherits: true;
  initial-value: 1;
}

@property --erx {
/* eye right x pos   */
  syntax: '<percentage>';
  inherits: true;
  initial-value: 0%;
}
@property --fx {
/* face x pos   */
  syntax: '<percentage>';
  inherits: true;
  initial-value: 0%;
}
@property --ealw {
/* ear left w    */
  syntax: '<number>';
  inherits: true;
  initial-value: 1;
}@property --earw {
/* ear right w    */
  syntax: '<number>';
  inherits: true;
  initial-value: 1;
}

@property --erh {
/* eye right height   */
  syntax: '<number>';
  inherits: true;
  initial-value: 1;
}
@property --mh {
/* mouth height   */
  syntax: '<number>';
  inherits: true;
  initial-value: 1;
}
@property --mw {
/* mouth width   */
  syntax: '<number>';
  inherits: true;
  initial-value: 1;
}
}

:root {
  --surface: #111;
    --c: white;
  --c2: #9ae3dc;
  --c3: magenta;
}

.ai-bot {
  scale: 4.2;
  width: 34px;
  aspect-ratio: 1;
  position: relative;
  display: grid;
  place-items: center;
  animation: blink 2.4s ease infinite, move-head 4.2s linear(0 0%, 0 2.27%, 0.02 4.53%, 0.04 6.8%, 0.06 9.07%, 0.1 11.33%, 0.14 13.6%, 0.25 18.15%, 0.39 22.7%, 0.56 27.25%, 0.77 31.8%, 1 36.35%, 0.89 40.9%, 0.85 43.18%, 0.81 45.45%, 0.79 47.72%, 0.77 50%, 0.75 52.27%, 0.75 54.55%, 0.75 56.82%, 0.77 59.1%, 0.79 61.38%, 0.81 63.65%, 0.85 65.93%, 0.89 68.2%, 1 72.7%, 0.97 74.98%, 0.95 77.25%, 0.94 79.53%, 0.94 81.8%, 0.94 84.08%, 0.95 86.35%, 0.97 88.63%, 1 90.9%, 0.99 93.18%, 0.98 95.45%, 0.99 97.73%, 1 100%)  infinite, mouth 1.2s ease-in infinite;

}

.ai-bot .head {
  background: linear-gradient(white 80%, #cccccc, white);
  border-radius: .375rem;
  position: absolute;
  width: 28px;
  height: 20px;
  left: 200px;
  top: -60px;
}

.ai-bot .head:before,
.ai-bot .head:after {
  content: '';
  position: absolute;
  left: -4px;
  top: 6px;
  width: 2px;
  height: 8px;
  background: white;
  border-radius: 2px 0 0 2px;
  scale: var(--ealw, 1) 1;
}

.ai-bot .head:after {
  right: -4px;
  left: unset;
  border-radius: 0 2px 2px 0;
  scale: var(--earw, 1) 1;

}

.ai-bot .head .face {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  inset: 0 3px;
  background: #111;
  translate: var(--fx) 0;
  border-radius: 4px;
  padding: 4px 4px 2px 4px;
  gap: 3px;
}

.ai-bot .head .face:before {
  content: '';
  background: white;
  position: absolute;
  height: 1px;
  width: 10px;
  top: -2px;
  border-radius: 2px 2px 0 0;
  mask: radial-gradient(circle at 50% 100%, transparent 45%, black 45%);
}

.ai-bot .head .face .eyes {
  display: flex;
  height: 8px;
  gap: 6px;
}

.ai-bot .head .face .eyes:before,
.ai-bot .head .face .eyes:after {
  content: '';
  width: 5px;
  height: 8px;
  scale: 1 var(--elh);
  filter: drop-shadow(0 0 2px #9ae3dc);
  background: repeating-linear-gradient(to bottom, white, white .25px, transparent .25px, transparent .6px), linear-gradient(to bottom, magenta, transparent 60%), #9ae3dc;
  border-radius: 1px;
  translate: var(--erx) 0;
}

.ai-bot .head .face .eyes:after {
  scale: 1 var(--erh);
  translate: var(--erx) 0;
}

.ai-bot .head .face .mouth {
  width: 10px;
  height: 2px;
  background: #9ae3dc;
  border-radius: 0 0 1px 1px;
  filter: drop-shadow(0 0 2px #9ae3dc);
  scale: var(--mw, 1) var(--mh, 1);
}

@media (max-width: 768px) {
  .ai-bot .head {
    left: 50px;
    top: -50px;
  }
}

@layer animations {

@keyframes blink {
  from,10%, to {
    --elh: 1;
    --erh: 1;
  }

  2% {
    --elh: .2;
  }

  8% {
    --erh: .1;
  }
}

@keyframes mouth {
  from, 30%,70%, to {
  --mh: 1;
    --mw: 1;
  }

  50% {
    --mh: .2;
    --mw: .5;
  }
}

@keyframes move-head {
  from, to {
    --fx: 0%;
    --erx: 0%;
    --ealw: 1;
    --earw: 1;
  }

  25% {
    --fx: -10%;
    --erx: -20%;
    --ealw: .8;
    --earw: 1.2;
  }

  75% {
    --fx: 10%;
    --erx: 20%;
    --ealw: 1.2;
    --earw: .8;
  }
}
}

/* Button styles */
.button-main {
    display: inline-block;
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--first-color);
    padding: 0.75rem 2.5rem;
    border-radius: .5rem;
    border: none;
    outline: none;
    font-size: var(--normal-font-size);
    cursor: pointer;
    margin-top: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    text-decoration: none;
}

.button-main:hover {
    background-color: rgba(255, 255, 255, 1);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    color: var(--first-color);
}

/* Three.js background styles */
#sphe-app {
    margin: 0;
    width: 100%;
    height: 100vh;
    background: radial-gradient(circle, rgba(255,255,255,1) 0%, rgba(0,0,0,0.5) 200%);
    position: relative;
    font-family: "Montserrat", serif;
    overflow: hidden;
}

#sphe-app .sphe-hero {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

#sphe-app .sphe-hero-text-background {
    position: relative;
    padding: 2rem 3rem;
    border-radius: 20px; 
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

#sphe-app .sphe-title-1,
#sphe-app .sphe-title-2 {
    margin: 0;
    padding: 0;
    color: #5361ff;
    text-transform: uppercase;
    line-height: 100%;
    user-select: none;
}

#sphe-app .sphe-title-1 {
    position: relative;
    z-index: 2;
    font-size: 100px;
    font-weight: 700;
}

#sphe-app .sphe-title-2 {
    z-index: 2;
    font-size: 80px;
    font-weight: 500;
}

@media screen and (max-width: 768px) {
    #sphe-app .sphe-title-1 {
        position: relative;
        z-index: 2;
        font-size: 50px;
        font-weight: 700;
    }
    #sphe-app .sphe-title-2 {
        z-index: 2;
        font-size: 50px;
        font-weight: 500;
    }

    #sphe-app .sphe-hero-text-background {
        padding: 1.5rem 2rem;
        margin: 0 1rem;
    }
}

#sphe-app #sphe-webgl-canvas {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;
    z-index: 1;
}
