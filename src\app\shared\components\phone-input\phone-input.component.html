<div class="phone-input__container" #phoneContainer>
  <label *ngIf="label" class="phone-input__label">{{ label }}</label>

  <div class="phone-input__wrapper" [class.phone-input__wrapper--error]="showError" [class.phone-input__wrapper--disabled]="disabled">
    <!-- Country Code Dropdown -->
    <div class="country-code__dropdown">
      <button
        type="button"
        class="country-code__trigger"
        [class.country-code__trigger--open]="isDropdownOpen"
        [disabled]="disabled"
        (click)="isDropdownOpen = !isDropdownOpen"
        [attr.aria-expanded]="isDropdownOpen"
        aria-haspopup="listbox">
        <img
          [src]="selectedCountry.flag"
          [alt]="selectedCountry.name"
          class="country-code__flag-img"
          onerror="this.style.display='none'">
        <span class="country-code__value">{{ selectedCountry.dialCode }}</span>
        <span class="country-code__arrow" [class.country-code__arrow--open]="isDropdownOpen">▼</span>
      </button>

      <!-- Simple Dropdown List -->
      <div
        *ngIf="isDropdownOpen"
        class="simple-dropdown">
        <div
          *ngFor="let country of countries; trackBy: trackByCountryCode"
          class="simple-dropdown-item"
          [class.active]="country.code === selectedCountry.code"
          (click)="selectCountry(country)">
          <img
            [src]="country.flag"
            [alt]="country.name"
            class="flag-img">
          <span class="dial-code">{{ country.dialCode }}</span>
          <span class="country-name">{{ country.name }}</span>
        </div>
      </div>
    </div>
    
    <!-- Phone Number Input -->
    <input 
      type="tel"
      class="phone-input__field"
      [placeholder]="getPlaceholder()"
      [disabled]="disabled"
      [value]="phoneNumber"
      (input)="onPhoneInput($event)"
      (blur)="onBlur()"
      autocomplete="tel">
  </div>
  
  <!-- Error Message -->
  <div *ngIf="showError && errorMessage" class="phone-input__error">
    {{ errorMessage }}
  </div>
</div>
