<div class="phone-input__container">
  <label *ngIf="label" class="phone-input__label">{{ label }}</label>
  
  <div class="phone-input__wrapper" [class.phone-input__wrapper--error]="showError" [class.phone-input__wrapper--disabled]="disabled">
    <!-- Country Code Dropdown -->
    <div class="country-code__dropdown">
      <button
        type="button"
        class="country-code__trigger"
        [class.country-code__trigger--open]="isDropdownOpen"
        [disabled]="disabled"
        (click)="toggleDropdown($event)"
        [attr.aria-expanded]="isDropdownOpen"
        aria-haspopup="listbox"
        #dropdownTrigger>
        <img
          [src]="selectedCountry.flag"
          [alt]="selectedCountry.name"
          class="country-code__flag-img"
          onerror="this.style.display='none'">
        <span class="country-code__value">{{ selectedCountry.dialCode }}</span>
        <span class="country-code__arrow" [class.country-code__arrow--open]="isDropdownOpen">▼</span>
      </button>


      <!-- Simple Dropdown List -->
      <div
        *ngIf="isDropdownOpen"
        class="simple-dropdown">
        <div
          *ngFor="let country of countries"
          class="simple-dropdown-item"
          [class.active]="country.code === selectedCountry.code"
          (click)="selectCountry(country, $event)">
          <img
            [src]="country.flag"
            [alt]="country.name"
            class="flag-img">
          <span class="dial-code">{{ country.dialCode }}</span>
          <span class="country-name">{{ country.name }}</span>
        </div>
      </div>
    </div>
    
    <!-- Phone Number Input -->
    <input 
      type="tel"
      class="phone-input__field"
      [placeholder]="getPlaceholder()"
      [disabled]="disabled"
      [value]="phoneNumber"
      (input)="onPhoneInput($event)"
      (blur)="onBlur()"
      autocomplete="tel">
  </div>
  
  <!-- Error Message -->
  <div *ngIf="showError && errorMessage" class="phone-input__error">
    {{ errorMessage }}
  </div>

  <!-- Debug Info -->
  <div class="text-xs text-gray-400 mt-1">
    Debug: Dropdown {{ isDropdownOpen ? 'OPEN' : 'CLOSED' }} | Countries: {{ countries.length }}
  </div>


</div>
