import { Component, Input, Output, EventEmitter, forwardRef, OnInit, OnDestroy, ElementRef, ViewChild } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, NG_VALIDATORS, AbstractControl, ValidationErrors, Validator } from '@angular/forms';
import { CountryService, Country } from '../../../core/services/country.service';

@Component({
  selector: 'app-phone-input',
  templateUrl: './phone-input.component.html',
  styleUrls: ['./phone-input.component.css'],
  standalone: false,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PhoneInputComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PhoneInputComponent),
      multi: true
    }
  ]
})
export class PhoneInputComponent implements ControlValueAccessor, Validator, OnInit, OnD<PERSON>roy {
  @Input() placeholder: string = '';
  @Input() label: string = 'Номер телефона';
  @Input() required: boolean = false;
  @Input() disabled: boolean = false;
  @Input() errorMessage: string = '';
  @Input() showError: boolean = false;

  @Output() phoneChange = new EventEmitter<string>();
  @Output() countryChange = new EventEmitter<Country>();

  @ViewChild('dropdownTrigger', { static: false }) dropdownTrigger!: ElementRef;

  countries: Country[] = [];
  selectedCountry: Country;
  phoneNumber: string = '';
  isDropdownOpen: boolean = false;
  isTouched: boolean = false;
  dropdownPosition = { top: '0px', left: '0px' };

  private onChange = (value: string) => {};
  private onTouched = () => {};

  constructor(private countryService: CountryService) {
    this.countries = this.countryService.getCountries();
    this.selectedCountry = this.countryService.getDefaultCountry();
    console.log('PhoneInputComponent initialized with countries:', this.countries.length);
  }

  ngOnInit() {
    // Close dropdown when clicking outside
    document.addEventListener('click', this.onDocumentClick.bind(this));
  }

  ngOnDestroy() {
    document.removeEventListener('click', this.onDocumentClick.bind(this));
  }

  // ControlValueAccessor implementation
  writeValue(value: string): void {
    if (value) {
      this.parsePhoneNumber(value);
    } else {
      this.phoneNumber = '';
    }
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  // Validator implementation
  validate(control: AbstractControl): ValidationErrors | null {
    if (!control.value && this.required) {
      return { required: true };
    }

    if (control.value && !this.isValidPhoneNumber(control.value)) {
      return { invalidPhone: true };
    }

    return null;
  }

  // Parse incoming phone number to extract country and number
  private parsePhoneNumber(fullNumber: string): void {
    if (!fullNumber) return;

    // Try to find matching country by dial code
    const matchingCountry = this.countries.find(country => 
      fullNumber.startsWith(country.dialCode)
    );

    if (matchingCountry) {
      this.selectedCountry = matchingCountry;
      this.phoneNumber = fullNumber.substring(matchingCountry.dialCode.length);
    } else {
      // Default behavior - assume it's a local number for current country
      this.phoneNumber = fullNumber;
    }
  }

  // Validate phone number format
  private isValidPhoneNumber(fullNumber: string): boolean {
    if (!fullNumber) return false;

    const country = this.countries.find(c => 
      fullNumber.startsWith(c.dialCode)
    );

    if (!country) return false;

    const localNumber = fullNumber.substring(country.dialCode.length);
    const digitsOnly = localNumber.replace(/\D/g, '');
    
    return digitsOnly.length === country.phoneLength;
  }

  // Toggle dropdown
  toggleDropdown(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    if (!this.disabled) {
      this.isDropdownOpen = !this.isDropdownOpen;
      console.log('Dropdown toggled:', this.isDropdownOpen); // Debug log
    }
  }

  // Select country
  selectCountry(country: Country, event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.selectedCountry = country;
    this.isDropdownOpen = false;
    this.updateValue();
    this.countryChange.emit(country);
    console.log('Country selected:', country.name); // Debug log
  }

  // Handle phone number input
  onPhoneInput(event: any): void {
    let value = event.target.value;
    
    // Remove all non-digit characters
    const digitsOnly = value.replace(/\D/g, '');
    
    // Limit to expected length for selected country
    const limitedDigits = digitsOnly.substring(0, this.selectedCountry.phoneLength);
    
    // Apply formatting if mask is available
    if (this.selectedCountry.phoneMask) {
      this.phoneNumber = this.applyMask(limitedDigits, this.selectedCountry.phoneMask);
    } else {
      this.phoneNumber = limitedDigits;
    }
    
    // Update input field
    event.target.value = this.phoneNumber;
    
    this.updateValue();
  }

  // Apply phone mask
  private applyMask(value: string, mask: string): string {
    let result = '';
    let valueIndex = 0;
    
    for (let i = 0; i < mask.length && valueIndex < value.length; i++) {
      if (mask[i] === '9') {
        result += value[valueIndex];
        valueIndex++;
      } else {
        result += mask[i];
      }
    }
    
    return result;
  }

  // Update form value
  private updateValue(): void {
    const fullNumber = this.selectedCountry.dialCode + this.phoneNumber.replace(/\D/g, '');
    this.onChange(fullNumber);
    this.phoneChange.emit(fullNumber);
  }

  // Handle blur event
  onBlur(): void {
    this.isTouched = true;
    this.onTouched();
  }

  // Handle document click to close dropdown
  private onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const dropdown = target.closest('.country-code__dropdown');

    // Only close if click is outside the dropdown
    if (!dropdown) {
      this.isDropdownOpen = false;
    }
  }

  // Get current full phone number
  getFullPhoneNumber(): string {
    return this.selectedCountry.dialCode + this.phoneNumber.replace(/\D/g, '');
  }

  // Get placeholder for current country
  getPlaceholder(): string {
    if (this.placeholder) {
      return this.placeholder;
    }
    return this.selectedCountry.phoneMask || '999999999';
  }

  // TrackBy function for performance
  trackByCountryCode(index: number, country: Country): string {
    return country.code;
  }
}
